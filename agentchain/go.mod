module agentchain

go 1.24.0

replace (
	// fix upstream GHSA-h395-qcrw-5vmq vulnerability.
	github.com/gin-gonic/gin => github.com/gin-gonic/gin v1.9.1
	// replace broken goleveldb
	github.com/syndtr/goleveldb => github.com/syndtr/goleveldb v1.0.1-0.20210819022825-2ae1ddf74ef7
	// replace broken vanity url
	nhooyr.io/websocket => github.com/coder/websocket v1.8.7
)

require (
	cosmossdk.io/api v0.9.2
	cosmossdk.io/client/v2 v2.0.0-beta.11
	cosmossdk.io/collections v1.2.1
	cosmossdk.io/core v0.11.3
	cosmossdk.io/depinject v1.2.1
	cosmossdk.io/errors v1.0.2
	cosmossdk.io/log v1.6.0
	cosmossdk.io/math v1.5.3
	cosmossdk.io/store v1.1.2
	cosmossdk.io/tools/confix v0.1.2
	cosmossdk.io/x/circuit v0.1.1
	cosmossdk.io/x/evidence v0.1.1
	cosmossdk.io/x/feegrant v0.1.1
	cosmossdk.io/x/nft v0.1.0
	cosmossdk.io/x/upgrade v0.2.0
	github.com/cometbft/cometbft v0.38.17
	github.com/cosmos/cosmos-db v1.1.1
	github.com/cosmos/cosmos-proto v1.0.0-beta.5
	github.com/cosmos/cosmos-sdk v0.53.2
	github.com/cosmos/gogoproto v1.7.0
	github.com/cosmos/ibc-go/v10 v10.2.0
	github.com/golang/protobuf v1.5.4
	github.com/gorilla/mux v1.8.1
	github.com/grpc-ecosystem/grpc-gateway v1.16.0
	github.com/spf13/cast v1.8.0
	github.com/spf13/cobra v1.9.1
	github.com/spf13/pflag v1.0.6
	github.com/spf13/viper v1.20.1
	github.com/stretchr/testify v1.10.0
	google.golang.org/genproto/googleapis/api v0.0.0-20250528174236-200df99c418a
	google.golang.org/grpc v1.72.2
	google.golang.org/protobuf v1.36.6
)

tool (
	
	"github.com/bufbuild/buf/cmd/buf"
	"github.com/cosmos/gogoproto/protoc-gen-gocosmos"
	"github.com/cosmos/gogoproto/protoc-gen-gogo"
	"github.com/cosmos/cosmos-proto/cmd/protoc-gen-go-pulsar"
	"google.golang.org/grpc/cmd/protoc-gen-go-grpc"
	"google.golang.org/protobuf/cmd/protoc-gen-go"
	"github.com/grpc-ecosystem/grpc-gateway/protoc-gen-grpc-gateway"
	"github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2"
	"golang.org/x/tools/cmd/goimports"
	"github.com/golangci/golangci-lint/cmd/golangci-lint"
)